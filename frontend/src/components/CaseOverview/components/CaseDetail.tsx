import { Separator } from "@radix-ui/react-separator";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import React, { createElement, useState, useEffect, useMemo, ReactNode, FormEvent } from "react";
import {
  useCombinedCardsQuery,
  useCreateUserCardMutation,
  // useDeleteUserCardMutation,
  useUpdateUserCardMutation,
  useBulkUpdateCardOrdersMutation,
  useUpdateSystemCardMutation,
  // useSyncUserCardMutation
} from "@/services/case-management/cardService";
import {
  SystemCard,
  UserCard,
  UserCardCreateRequest,
  BulkCardOrderUpdate,
  CardOrderUpdate,
  CaseAgeData,
  PendingStatuteData,
  PolicyLimitsData,
  LiensData,
  HealthProvidersData,
  IncidentReportData,
  PropertyDamageData,
  HealthInsuranceData,
  LostWagesData,
  AdvancedLoansData,
  PoliceReportStatusData,
  MedicalTreatmentData,
  ClientAgeData,
  PipMedpayData,
  CaseCostData,
} from "@/type/case-management/cardTypes";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import CustomCKEditor from "@/components/CKEditor";
import {
  Plus,
  X,
  GripVertical,
  Pencil,
  LucideIcon,
  MoreVertical,
  Eye,
  EyeOff,
  Settings,
  ArrowLeft,
  ArrowRight,
  ExternalLink,
  FileText,
  Image,
  Share2,
  NotebookPen,
  Folders,
  UserRound,
  Shield,
  ShieldCheck,
  Stethoscope,
  Edit,
  Video,
  Check,
  PinOff,
  Film,
  AlertCircle,
  Circle,
  DollarSign,
} from "lucide-react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { cn } from "@/lib/utils";
// import RichTextViewer from "@/components/ui/RichTextViewer";
import PlaintiffInsurance from "./PlaintiffInsurance";
import DefendantInsurance from "./DefendantInsurance";
import HealthProvider from "./HealthProvider";
// import ContactDetails from "./ContactDetails";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import CaseIncidentSummary from "./CaseIncidentSummary";
import { useIncidentDetailsQuery } from '@/services/incidentService';
import LitigationEvents from './LitigationEvents';
import { usePinnedFileQuery, usePinFileMutation } from "@/services/case-management/newDocumentManagement";
// import { useLinkedCasesQuery } from '@/services/case-management/linkCaseService';
import { CopyNumber } from "@/components/ui/copy-number";
import TaskCards from "./TaskCards";
import { useAccidentRecreationVideoQuery, useCreateAccidentRecreationVideoMutation, useUpdateAccidentRecreationVideoMutation } from "@/services/accidentRecreationService";
import VideoPlayer from "@/components/ui/VideoPlayer";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { useTreatmentProvidersQuery } from '@/services/case-management/medicalTreatmentService';
import type { TreatmentProvider } from '@/type/case-management/medicalTreatmentTypes';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { content } from "html2canvas/dist/types/css/property-descriptors/content";

interface CaseDetailProps {
  caseId: string;
  setActiveTab?: (tab: string) => void;
}

interface ImageModalProps {
  imageUrl: string;
  imageName: string;
  onClose: () => void;
}

const ImageModal = ({ imageUrl, imageName, onClose }: ImageModalProps) => {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80" onClick={onClose}>
      <div className="relative max-w-5xl max-h-[90vh] w-full p-4">
        <button
          className="absolute top-2 right-2 bg-white rounded-full p-2 shadow-md z-10 hover:bg-gray-100"
          onClick={(e) => {
            e.stopPropagation();
            onClose();
          }}
        >
          <X className="h-5 w-5 text-gray-600" />
        </button>
        <div className="bg-white/5 p-1 rounded-lg shadow-2xl overflow-hidden">
          <img
            src={imageUrl}
            alt={imageName}
            className="max-w-full max-h-[80vh] object-contain mx-auto rounded-md"
            onClick={(e) => e.stopPropagation()}
          />
        </div>
        <div className="mt-3 text-center text-white text-sm font-medium">
          {imageName}
        </div>
      </div>
    </div>
  );
};

interface ImageSliderProps {
  files: Array<{ id: string; name: string; view_url: string }>;
  onImageClick: (file: { view_url: string; name: string }) => void;
  onOpenInNewTab: (url: string) => void;
  isImageFile: (fileName: string) => boolean;
}

const ImageSlider = ({ files, onImageClick, onOpenInNewTab, isImageFile }: ImageSliderProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? files.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev === files.length - 1 ? 0 : prev + 1));
  };

  const currentFile = files[currentIndex];

  if (!files || files.length === 0) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-lg">
        <div className="flex flex-col items-center justify-center">
          <FileText className="h-8 w-8 text-gray-400" />
          <span className="text-xs text-gray-500 mt-1">No Files</span>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      <div className="w-full h-full flex items-center justify-center">
        <div className="w-full h-full rounded-lg overflow-hidden cursor-pointer border-2 border-white shadow-lg hover:opacity-90 transition-all relative group">
          {isImageFile(currentFile.name) ? (
            <img
              src={currentFile.view_url}
              alt={currentFile.name}
              className="w-full h-full object-contain bg-gray-50"
              onClick={() => onImageClick(currentFile)}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-100">
              <Image className="h-6 w-6 text-gray-400" />
            </div>
          )}

          {/* Navigation Buttons - Only show if more than one file */}
          {files.length > 1 && (
            <>
              {/* Left half for previous button */}
              <div className="absolute left-0 top-0 w-1/2 h-full group/left">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePrevious();
                  }}
                  className="absolute left-0 top-1/2 -translate-y-1/2 p-1 bg-white/80 rounded-r opacity-0 group-hover/left:opacity-100 transition-opacity hover:bg-white"
                >
                  <ArrowLeft className="h-4 w-4 text-gray-600" />
                </button>
              </div>

              {/* Right half for next button */}
              <div className="absolute right-0 top-0 w-1/2 h-full group/right">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleNext();
                  }}
                  className="absolute right-0 top-1/2 -translate-y-1/2 p-1 bg-white/80 rounded-l opacity-0 group-hover/right:opacity-100 transition-opacity hover:bg-white"
                >
                  <ArrowRight className="h-4 w-4 text-gray-600" />
                </button>
              </div>
            </>
          )}

          {/* External Link Button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onOpenInNewTab(currentFile.view_url);
            }}
            className="absolute top-1 right-1 p-1.5 bg-white rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <ExternalLink className="h-4 w-4 text-gray-600" />
          </button>
        </div>
      </div>

      {/* File Counter - Only show if more than one file */}
      {files.length > 1 && (
        <div className="absolute bottom-0 left-1/2 -translate-x-1/2 bg-white/80 px-2 py-1 rounded-full text-xs">
          {currentIndex + 1}/{files.length}
        </div>
      )}
    </div>
  );
};

interface CreateCardDialogProps {
  caseId: string;
  onSuccess: () => void;
  mode?: 'create' | 'edit';
  initialData?: {
    id: number;
    title: string;
    content: string;
  };
  children?: ReactNode;
}

export function CreateCardDialog({ caseId, onSuccess, mode = 'create', initialData }: CreateCardDialogProps) {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    content: ''
  });
  const createCard = useCreateUserCardMutation(caseId);
  const updateCard = useUpdateUserCardMutation(caseId);
  const { toast } = useToast();

  useEffect(() => {
    if (open && mode === 'edit' && initialData) {
      setFormData({
        title: initialData.title,
        content: initialData.content
      });
    } else if (!open) {
      setFormData({
        title: '',
        content: ''
      });
    }
  }, [open, mode, initialData]);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!formData.title || !formData.content || formData.content === '[]') {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    try {
      if (mode === 'edit' && initialData) {
        await updateCard.mutateAsync({
          cardId: initialData.id,
          data: {
            title: formData.title,
            content: formData.content
          }
        });
      } else {
        const cardData: UserCardCreateRequest = {
          title: formData.title,
          content: formData.content,
          is_pinned: false,
          is_visible: true,
        };
        await createCard.mutateAsync(cardData);
      }

      setFormData({
        title: '',
        content: ''
      });
      setOpen(false);
      onSuccess();
    } catch {
      toast({
        title: "Error",
        description: `Failed to ${mode} note. Please try again.`,
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {mode === 'create' ? (
          <Button variant="link" className="border-none text-green-600 hover:text-green-700">
            <Plus className="h-4 w-4 mr-2" />
            Add Note
          </Button>
        ) : (
          <Button variant="link" className="border-none text-green-600 hover:text-green-700">
            <Pencil className="h-4 w-4" />
          </Button>
        )}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{mode === 'create' ? 'Add New Note' : 'Edit Note'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            placeholder="Title"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          />
          <CustomCKEditor
            initialValue={formData.content}
            onChange={(value: string) => {
              setFormData(prev => ({ ...prev, content: value }));
            }}
            placeholder="Take a note..."
            minHeight="100px"
            className="min-h-[100px] resize-none bg-gray-50"
          />
          <div className="flex justify-end gap-2">
            <Button type="submit">{mode === 'create' ? 'Create' : 'Update'}</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// interface UserCardItemProps {
//   card: UserCard;
//   onDelete: (cardId: number) => Promise<void>;
//   caseId: string;
//   onSuccess: () => void;
//   linkedCases?: string[];
//   onSync?: (cardId: number) => Promise<void>;
//   syncingCardId?: number | null;
//   isFirstCard?: boolean;
// }

// function UserCardItem({ card, onDelete, caseId, onSuccess, linkedCases = [], onSync, syncingCardId, isFirstCard }: UserCardItemProps) {
//   const [isDeleting, setIsDeleting] = useState(false);
//   const { toast } = useToast();

//   const handleDelete = async () => {
//     try {
//       setIsDeleting(true);
//       await onDelete(card.id);
//     } catch {
//       toast({
//         title: "Error",
//         description: "Failed to delete note. Please try again.",
//         variant: "destructive",
//       });
//     } finally {
//       setIsDeleting(false);
//     }
//   };

//   return (
//     <Card className="bg-[#E8F0EA] hover:shadow-xl transition-all duration-200 backdrop-blur-sm border border-[#E8F0EA] w-full relative group p-1">
//       <CardContent className="relative w-full p-2 bg-[#E8F0EA]">
//         <div className="absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
//           {linkedCases.length > 0 && onSync && (
//             <Button
//               variant="ghost"
//               size="icon"
//               className="h-6 w-6"
//               onClick={() => onSync(card.id)}
//               disabled={syncingCardId === card.id}
//               title="Sync to linked cases"
//             >
//               <Share2 className="h-4 w-4 text-gray-500 hover:text-gray-700" />
//             </Button>
//           )}
//           {/* <Button
//             variant="ghost"
//             size="icon"
//             className="h-6 w-6"
//             onClick={() => onTogglePin(card.id, !card.is_pinned)}
//           >
//             {card.is_pinned ? (
//               <PinOff className="h-4 w-4" />
//             ) : (
//               <Pin className="h-4 w-4" />
//             )}
//           </Button> */}
//           <CreateCardDialog
//             caseId={caseId}
//             mode="edit"
//             initialData={{
//               id: card.id,
//               title: card.title,
//               content: card.content
//             }}
//             onSuccess={onSuccess}
//           >
//             <Button
//               variant="ghost"
//               size="icon"
//               className="h-6 w-6"
//             >
//               <Pencil className="h-4 w-4 text-gray-500 hover:text-gray-700" />
//             </Button>
//           </CreateCardDialog>
//           <Button
//             variant="ghost"
//             size="icon"
//             className="h-6 w-6"
//             onClick={handleDelete}
//             disabled={isDeleting}
//           >
//             <X className="h-4 w-4 text-gray-500 hover:text-gray-700" />
//           </Button>
//         </div>

//         <h3 className="text-sm text-[#4B5563] mb-2 tracking-tight truncate pr-20">
//           {card.title}
//         </h3>
//         <div className="space-y-1">
//           <div className={`text-md ${isFirstCard ? 'text-red-600 font-bold' : 'text-black-600'} break-words line-clamp-3`}>
//             {card.content ? <RichTextViewer data={card.content} /> : ''}
//           </div>
//         </div>

//         <div className="flex items-center justify-between pt-2">
//           <div className="text-sm text-[#4B5563] truncate">
//             {card.created_by?.name && `Added by ${card.created_by.name}`}
//           </div>
//         </div>
//       </CardContent>
//     </Card>
//   );
// }

interface SortableCardProps {
  id: number;
  children: ReactNode;
}

function SortableCard({ id, children }: SortableCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: id });

  if (!id) {
    return <div className="relative w-full">{children}</div>;
  }

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    position: "relative" as const,
    zIndex: isDragging ? 50 : 'auto',
    width: '100%',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="relative group hover:z-20 w-full"
    >
      <div
        {...listeners}
        {...attributes}
        className="absolute right-0 top-0 bottom-0 w-8 flex items-center justify-center opacity-0 group-hover:opacity-100 mr-[-1rem] cursor-grab active:cursor-grabbing z-30"
      >
        <div className="p-1.5 hover:bg-gray-100 rounded">
          <GripVertical className="h-4 w-4 text-gray-400" />
        </div>
      </div>
      {children}
    </div>
  );
}

export interface InfoItemType {
  icon: LucideIcon | (() => JSX.Element);
  label: string;
  value: string | ReactNode;
}

interface CardVisibilityMenuProps {
  card: SystemCard;
  caseId: string;
  onVisibilityChange: () => void;
}

function CardVisibilityMenu({ card, caseId, onVisibilityChange }: CardVisibilityMenuProps) {
  const updateSystemCard = useUpdateSystemCardMutation(caseId);
  const { toast } = useToast();

  const handleVisibilityToggle = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent the click from bubbling up to the card
    try {
      await updateSystemCard.mutateAsync({
        cardId: card.id,
        data: { is_visible: !card.is_visible }
      });
      onVisibilityChange();
    } catch {
      toast({
        title: "Error",
        description: "Failed to update card visibility",
        variant: "destructive",
      });
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
        <Button variant="ghost" size="icon" className="h-6 w-6 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <MoreVertical className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
        <DropdownMenuLabel>Card Settings</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleVisibilityToggle}>
          {card.is_visible ? (
            <>
              <EyeOff className="mr-2 h-4 w-4" />
              Hide Card
            </>
          ) : (
            <>
              <Eye className="mr-2 h-4 w-4" />
              Show Card
            </>
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface HiddenCardsMenuProps {
  hiddenCards: SystemCard[];
  caseId: string;
  onVisibilityChange: () => void;
}

function HiddenCardsMenu({ hiddenCards, caseId, onVisibilityChange }: HiddenCardsMenuProps) {
  const updateSystemCard = useUpdateSystemCardMutation(caseId);
  const { toast } = useToast();

  const handleShowCard = async (cardId: number) => {
    try {
      await updateSystemCard.mutateAsync({
        cardId,
        data: { is_visible: true }
      });
      onVisibilityChange();
      toast({
        title: "Success",
        description: "Card restored successfully",
      });
    } catch {
      toast({
        title: "Error",
        description: "Failed to restore card",
        variant: "destructive",
      });
    }
  };

  if (hiddenCards.length === 0) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="ml-2">
          <Settings className="h-4 w-4 mr-2" />
          Manage Cards
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Hidden Cards</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {hiddenCards.map((card) => (
          <DropdownMenuItem key={card.id} onClick={() => handleShowCard(card.id)}>
            <Eye className="mr-2 h-4 w-4" />
            Show {card.title}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function getCardIcon(cardType: string) {
  switch (cardType) {
    case "THIRD_PARTY_POLICY_LIMIT":
      return Shield;
    case "PENDING_STATUTE":
      return FileText;
    case "ATTORNEY_LIENS":
    case "MISC_LIENS":
      return NotebookPen;
    case "HEALTH_PROVIDERS":
      return Stethoscope;
    case "HEALTH_INSURANCE":
    case "PIP_MEDPAY":
      return ShieldCheck;
    case "LOST_WAGES":
      return UserRound;
    case "ADVANCED_LOANS":
      return Share2;
    case "MEDICAL_TREATMENT":
      return Stethoscope;
    case "CLIENT_AGE":
      return UserRound;
    case "CASE_COST":
      return DollarSign;
    default:
      return FileText;
  }
}

function SystemCardItem({
  card,
  onClick,
  caseId,
  onVisibilityChange,
}: {
  card: SystemCard;
  onClick?: () => void;
  caseId: string;
  onVisibilityChange: () => void;
}): JSX.Element {
  const formatCurrency = (amount: string | number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(typeof amount === "string" ? parseFloat(amount) : amount);
  };

  const getCardDetails = (
    card: SystemCard
  ): { value: string; subtext?: string; status?: string } => {
    if (!card.data) return { value: "N/A" };

    switch (card.card_type) {
      case "CASE_AGE": {
        const data = card.data as CaseAgeData;
        return {
          value: data?.age_days ? `${data.age_days} days` : "0 days",
          subtext: "since case opened",
        };
      }
      case "PENDING_STATUTE": {
        const data = card.data as PendingStatuteData;
        const days = Math.max(0, data?.days_remaining || 0);
        const status = days < 30 ? "critical" : days < 90 ? "warning" : "good";
        return {
          value: `${days} days`,
          subtext: "until statute expires",
          status,
        };
      }
      case "THIRD_PARTY_POLICY_LIMIT": {
        const data = card.data as PolicyLimitsData;
        if (!data?.defendants || !Array.isArray(data.defendants)) {
          return {
            value: "No limits",
            subtext: "No defendants",
          };
        }

        const defendantSummaries = data.defendants.map(d => ({
          name: d?.name || "Unnamed",
          limit: d?.limit || "Not set"
        }));

        const value = defendantSummaries.map(d =>
          `${d.name}: ${d.limit}`
        ).join('\n');

        return {
          value,
          subtext: "Policy Limits",
        };
      }
      case "ATTORNEY_LIENS":
      case "MISC_LIENS": {
        const data = card.data as LiensData;
        const count = data?.count || 0;
        const total = data?.total || "0";
        if (count === 0) return { value: "None", subtext: "No liens recorded" };
        return {
          value: formatCurrency(total),
          subtext: `${count} lien${count !== 1 ? "s" : ""} recorded`,
        };
      }
      case "HEALTH_PROVIDERS": {
        const data = card.data as HealthProvidersData;
        const count = data?.count || 0;
        return {
          value: count.toString(),
          subtext: `provider${count !== 1 ? "s" : ""} involved`,
        };
      }
      case "INCIDENT_REPORT": {
        const data = card.data as IncidentReportData;
        return {
          value: data.status || "Pending",
          subtext: "Report status",
        };
      }
      case "POLICE_REPORT_STATUS": {
        const data = card.data as PoliceReportStatusData;
        return {
          value: data.status_display || "Not Started",
          subtext: data.last_updated ? `Updated ${new Date(data.last_updated).toLocaleDateString()}` : "Not started yet",
          status: data.status === "REQUESTED" ? "warning" :
            data.status === "RECEIVED" ? "good" :
              data.status === "NOT_AVAILABLE" ? "critical" : undefined
        };
      }
      case "PROPERTY_DAMAGE": {
        const data = card.data as PropertyDamageData;
        if (!data) return { status: "Not Available", value: "Not Available", subtext: "No damage data" };

        const formatCurrency = (amount: string | null) => {
          if (!amount) return "Not set";
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
            maximumFractionDigits: 0,
          }).format(parseFloat(amount));
        };

        const getVehicleInfo = () => {
          if (!data.vehicle_info) return "";
          const { make, model, year } = data.vehicle_info;
          if (!make && !model && !year) return "";
          return [year, make, model].filter(Boolean).join(" ");
        };

        const vehicleInfo = getVehicleInfo();
        const estimate = formatCurrency(data.estimate);

        let value = data.damage_type || "Not Available";
        if (data.total_loss) {
          value = "Total Loss";
        }

        let subtext = "Damage Assessment";
        if (estimate !== "Not set") {
          subtext = `Estimate: ${estimate}`;
        }
        if (vehicleInfo) {
          subtext = `${vehicleInfo} - ${subtext}`;
        }

        return {
          value,
          subtext,
          status: data.total_loss ? "critical" :
            data.damage_type === "Major" ? "warning" :
              data.damage_type === "Minor" ? "good" : undefined
        };
      }
      case "HEALTH_INSURANCE": {
        const data = card.data as HealthInsuranceData;
        const formatCurrency = (amount: string) => {
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(parseFloat(amount || "0"));
        };

        if (data.has_health_insurance && data.total_lien_amount && parseFloat(data.total_lien_amount) > 0) {
          return {
            value: formatCurrency(data.total_lien_amount),
            subtext: `${data.health_insurance_count || 0} insurance provider${data.health_insurance_count !== 1 ? 's' : ''}`,
            status: "good",
          };
        }

        return {
          value: data.has_health_insurance ? "Yes" : "No",
          subtext: `${data.health_insurance_count || 0} insurance provider${data.health_insurance_count !== 1 ? 's' : ''}`,
          status: data.has_health_insurance ? "good" : "warning",
        };
      }
      case "LOST_WAGES": {
        const data = card.data as LostWagesData;
        if (!data) return { value: "N/A", subtext: "No wage data" };

        if (!data.is_employed) {
          return { value: "Not Employed", subtext: "No lost wages claim" };
        }

        const formatCurrency = (amount: string) => {
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
            maximumFractionDigits: 0,
          }).format(parseFloat(amount || "0"));
        };

        const totalLostWages = formatCurrency(data.total_lost_wages || "0");
        const employerInfo = data.employers?.[0];
        let subtext = `${data.employer_count || 0} employer${data.employer_count !== 1 ? 's' : ''}`;

        if (employerInfo) {
          const status = employerInfo.status === "PENDING" ? "Pending" :
            employerInfo.status === "VERIFIED" ? "Verified" :
              employerInfo.status === "REJECTED" ? "Rejected" :
                employerInfo.status;
          subtext = `${employerInfo.company || 'Not Available'} - ${status}`;
        }

        return {
          value: totalLostWages,
          subtext,
          status: employerInfo?.status === "VERIFIED" ? "good" :
            employerInfo?.status === "REJECTED" ? "critical" :
              employerInfo?.status === "PENDING" ? "warning" : undefined
        };
      }
      case "ADVANCED_LOANS": {
        const advancedLoansData = card.data as AdvancedLoansData;
        return {
          value: formatCurrency(advancedLoansData.amount || "0"),
          subtext: "Advanced loans",
        };
      }
      case "MEDICAL_TREATMENT": {
        const data = card.data as MedicalTreatmentData;
        if (!data) return { value: "N/A", subtext: "No treatment data" };

        const formatCurrency = (amount: string) => {
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
            maximumFractionDigits: 0,
          }).format(parseFloat(amount));
        };

        return {
          value: formatCurrency(data.total_original_bills || "0"),
          subtext: `${data.provider_count || 0} provider${data.provider_count !== 1 ? 's' : ''}`,
        };
      }
      case "CLIENT_AGE": {
        const data = card.data as ClientAgeData;
        return {
          value: data.display || "Not Available",
          subtext: "Client's age",
        };
      }
      case "PIP_MEDPAY": {
        const data = card.data as PipMedpayData;
        const formatCurrency = (amount: string) => {
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
            maximumFractionDigits: 0,
          }).format(parseFloat(amount || "0"));
        };

        return {
          value: data.has_pip_medpay ? formatCurrency(data.total_amount) : "No",
          subtext: data.has_pip_medpay ? `${data.pip_medpay_count || 0} provider${data.pip_medpay_count !== 1 ? 's' : ''}` : "No PIP/MedPay",
          status: data.has_pip_medpay ? "good" : "warning",
        };
      }
      case "CASE_COST": {
        const data = card.data as CaseCostData;
        return {
          value: formatCurrency(data.total_amount),
          subtext: `${data.cost_count} cost${data.cost_count !== 1 ? 's' : ''}`,
          status: parseFloat(data.pending_amount) > 0 ? "warning" : "good"
        };
      }
      default:
        return { value: "N/A" };
    }
  };

  const getValueColor = () => {
    return "text-black";
  };

  const getDetailedContent = (card: SystemCard) => {
    if (!card.data) return null;

    const baseContentStyle = "p-3 bg-white/80 rounded-lg border border-gray-100 shadow-sm";

    switch (card.card_type) {
      case "THIRD_PARTY_POLICY_LIMIT": {
        const data = card.data as PolicyLimitsData;
        if (!data.defendants || !Array.isArray(data.defendants)) {
          return (
            <div className="space-y-2">
              <h4 className="font-sm text-gray-900">Policy Limits Details</h4>
              <div className={baseContentStyle}>
                <div className="text-gray-500">No defendants</div>
              </div>
            </div>
          );
        }

        return (
          <div className="space-y-3">
            <h4 className="font-sm text-gray-900">Policy Limits Details</h4>
            <div className="space-y-2">
              {data.defendants.map((defendant, index) => (
                <div key={index} className={baseContentStyle}>
                  <div className="text-gray-500 mb-1">
                    {defendant.name || "Unnamed Defendant"}
                  </div>
                  <div className="grid gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-xs text-gray-500">Insurance Company</span>
                      <span className="font-medium">{defendant.company || 'Not specified'}</span>
                    </div>
                    {defendant.policy_number && (
                      <div className="flex justify-between">
                        <span className="text-xs text-gray-500">Policy Number</span>
                        <span className="font-medium">{defendant.policy_number ? <CopyNumber value={defendant.policy_number} /> : "-"}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      }
      case "PROPERTY_DAMAGE": {
        const data = card.data as PropertyDamageData;
        const formatCurrency = (amount: string | null) => {
          if (!amount) return "Not set";
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
            maximumFractionDigits: 0,
          }).format(parseFloat(amount));
        };

        return (
          <div className="space-y-3">
            <h4 className="font-sm text-gray-900">Property Damage Details</h4>
            <div className="space-y-2">
              <div className={baseContentStyle}>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Status</span>
                    <Badge variant="outline" className={cn(
                      "font-medium",
                      data.total_loss ? "bg-red-50 text-red-600 border-red-200" :
                        data.damage_type === "Major" ? "bg-orange-50 text-orange-600 border-orange-200" :
                          "bg-emerald-50 text-emerald-600 border-emerald-200"
                    )}>
                      {data.total_loss ? "Total Loss" : data.damage_type || "Not Available"}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Estimate</span>
                    <span className="font-semibold text-green-600">{formatCurrency(data.estimate)}</span>
                  </div>
                  {data.final && (
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">Final Amount</span>
                      <span className="font-semibold text-green-600">{formatCurrency(data.final)}</span>
                    </div>
                  )}
                </div>
              </div>
              {data.vehicle_info && (data.vehicle_info.make || data.vehicle_info.model || data.vehicle_info.year) && (
                <div className={baseContentStyle}>
                  <h5 className="text-sm font-medium text-gray-700 mb-3">Vehicle Information</h5>
                  <div className="grid grid-cols-3 gap-3 text-sm">
                    <div>
                      <div className="text-gray-500 mb-1">Year</div>
                      <div className="font-medium">{data.vehicle_info.year || "—"}</div>
                    </div>
                    <div>
                      <div className="text-gray-500 mb-1">Make</div>
                      <div className="font-medium">{data.vehicle_info.make || "—"}</div>
                    </div>
                    <div>
                      <div className="text-gray-500 mb-1">Model</div>
                      <div className="font-medium">{data.vehicle_info.model || "—"}</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      }
      case "LOST_WAGES": {
        const data = card.data as LostWagesData;
        if (!data) return (
          <div className="space-y-2">
            <h4 className="font-sm text-gray-900">Lost Wages Details</h4>
            <div className={baseContentStyle}>
              <div className="text-xs text-gray-500">Not currently employed</div>
            </div>
          </div>
        );

        const formatCurrency = (amount: string) => {
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
            maximumFractionDigits: 0,
          }).format(parseFloat(amount));
        };

        return (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-sm text-gray-900">Lost Wages Details</h4>
              <Badge variant="outline" className="bg-gray-50">
                Total: {formatCurrency(data.total_lost_wages || "0")}
              </Badge>
            </div>
            <div className="space-y-2">
              {data?.employers?.map((employer, index) => (
                <div key={index} className={baseContentStyle}>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="font-sm text-gray-900">{employer.company}</span>
                      <Badge variant="outline" className={cn(
                        "font-medium",
                        employer.status === "VERIFIED" ? "bg-emerald-50 text-emerald-600 border-emerald-200" :
                          employer.status === "REJECTED" ? "bg-red-50 text-red-600 border-red-200" :
                            "bg-orange-50 text-orange-600 border-orange-200"
                      )}>
                        {employer.status === "VERIFIED" ? "Verified" :
                          employer.status === "REJECTED" ? "Rejected" : "Pending"}
                      </Badge>
                    </div>
                    <div className="grid gap-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-xs text-gray-500">Lost Wages</span>
                        <span className="font-semibold text-green-600">{formatCurrency(employer.lost_wages || "0")}</span>
                      </div>
                      {employer.hours_missed && (
                        <div className="flex justify-between">
                          <span className="text-xs text-gray-500">Hours Missed</span>
                          <span className="font-medium">{employer.hours_missed}</span>
                        </div>
                      )}
                      {employer.weeks_missed && (
                        <div className="flex justify-between">
                          <span className="text-xs text-gray-500">Weeks Missed</span>
                          <span className="font-medium">{employer.weeks_missed}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      }
      case "ATTORNEY_LIENS":
      case "MISC_LIENS": {
        const data = card.data as LiensData;
        const formatCurrency = (amount: string) => {
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
            maximumFractionDigits: 0,
          }).format(parseFloat(amount));
        };

        const isAttorneyLien = card.card_type === "ATTORNEY_LIENS";
        const title = isAttorneyLien ? "Attorney Liens Details" : "Miscellaneous Liens Details";
        const badgeColor = isAttorneyLien ? "amber" : "yellow";

        if (data.count === 0) {
          return (
            <div className="space-y-2">
              <h4 className="font-sm text-gray-900">{title}</h4>
              <div className={baseContentStyle}>
                <div className="text-gray-500">No liens</div>
              </div>
            </div>
          );
        }

        return (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-sm text-gray-900">{title}</h4>
              <Badge variant="outline" className={`bg-${badgeColor}-50 text-${badgeColor}-600 border-${badgeColor}-200`}>
                {data.count} {data.count === 1 ? 'Lien' : 'Liens'}
              </Badge>
            </div>
            <div className={baseContentStyle}>
              <div className="space-y-3">
                <div>
                  <div className="text-xs text-gray-500 mb-1">Total Amount</div>
                  <div className="text-2xl font-semibold text-green-600">
                    {formatCurrency(data.total)}
                  </div>
                </div>
                <div className="text-xs text-gray-400">
                  Click to view detailed breakdown
                </div>
              </div>
            </div>
          </div>
        );
      }
      case "MEDICAL_TREATMENT": {
        const data = card.data as MedicalTreatmentData;
        if (!data) return null;

        const formatCurrency = (amount: string | null) => {
          if (!amount) return "Not set";
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
            maximumFractionDigits: 0,
          }).format(parseFloat(amount));
        };

        const getStatusBadge = (status: string) => {
          const statusMap = {
            "COMPLETE": { text: "Complete", class: "bg-emerald-50 text-emerald-600 border-emerald-200" },
            "IN_PROGRESS": { text: "In Progress", class: "bg-blue-50 text-[#060216]-600 border-blue-200" },
            "NOT_AVAILABLE": { text: "Not Available", class: "bg-gray-50 text-gray-600 border-gray-200" },
          };
          const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap.NOT_AVAILABLE;
          return (
            <Badge variant="outline" className={cn("font-medium", statusInfo.class)}>
              {statusInfo.text}
            </Badge>
          );
        };

        return (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-sm text-gray-900">Medical Treatment Details</h4>
              <Badge variant="outline" className="bg-blue-50 text-[#060216]-600 border-blue-200">
                Total: {formatCurrency(data.total_original_bills)}
              </Badge>
            </div>
            <div className="space-y-2">
              {data.providers?.map((provider, index) => (
                <div key={index} className={baseContentStyle}>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="font-sm text-gray-900">
                        {provider.provider_name}
                      </span>
                      {getStatusBadge(provider.treatment_status)}
                    </div>
                    {provider.original_bill && (
                      <div className="flex justify-between text-sm">
                        <span className="text-xs text-gray-500">Original Bill</span>
                        <span className="font-semibold text-green-600">
                          {formatCurrency(provider.original_bill)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {(!data.providers || data.providers.length === 0) && (
                <div className={baseContentStyle}>
                  <div className="text-gray-500">No providers</div>
                </div>
              )}
            </div>
          </div>
        );
      }
      case "CLIENT_AGE": {
        const data = card.data as ClientAgeData;
        return (
          <div className="space-y-3">
            <h4 className="font-md text-gray-900">Client Age Details</h4>
            <div className={baseContentStyle}>
              <div className="space-y-3">
                <div className="grid gap-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Years</span>
                    <span className="font-medium">{data.years}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Months</span>
                    <span className="font-medium">{data.months}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Days</span>
                    <span className="font-medium">{data.days}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      }

      case "HEALTH_INSURANCE": {
        const data = card.data as HealthInsuranceData;
        return (
          <div className="space-y-3">
            <h4 className="font-sm text-gray-900">Health Insurance Details</h4>
            {data.health_insurances && data.health_insurances.length > 0 ? (
              <div className="space-y-2">
                {data.health_insurances.map((insurance, index) => (
                  <div key={index} className={baseContentStyle}>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="font-sm text-gray-900">{insurance.insurance_company}</span>
                        <Badge variant="outline" className={cn(
                          "font-medium",
                          insurance.medicare === "True" ? "bg-blue-50 text-blue-600 border-blue-200" :
                            insurance.erisa === "True" ? "bg-purple-50 text-purple-600 border-purple-200" :
                              "bg-gray-50 text-gray-600 border-gray-200"
                        )}>
                          {insurance.medicare === "True" ? "Medicare" :
                            insurance.erisa === "True" ? "ERISA" :
                              insurance.plan_type || "Standard"}
                        </Badge>
                      </div>
                      {insurance.policy_number && (
                        <div className="flex justify-between text-sm">
                          <span className="text-xs text-gray-500">Policy Number</span>
                          <span className="font-medium">{insurance.policy_number && <CopyNumber value={insurance.policy_number} />}</span>
                        </div>
                      )}
                      {insurance.total_lien && (
                        <div className="flex justify-between text-sm">
                          <span className="text-xs text-gray-500">Total Lien</span>
                          <span className="font-semibold text-green-600">
                            {new Intl.NumberFormat("en-US", {
                              style: "currency",
                              currency: "USD",
                              maximumFractionDigits: 2,
                            }).format(parseFloat(insurance.total_lien))}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={baseContentStyle}>
                <div className="text-gray-500">No health insurance</div>
              </div>
            )}
          </div>
        );
      }
      case "PIP_MEDPAY": {
        const data = card.data as PipMedpayData;
        return (
          <div className="space-y-3">
            <h4 className="font-sm text-gray-900">PIP/MedPay Details</h4>
            {data.pip_medpay_details && data.pip_medpay_details.length > 0 ? (
              <div className="space-y-2">
                {data.pip_medpay_details.map((pip, index) => (
                  <div key={index} className={baseContentStyle}>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="font-sm text-gray-900">{pip.insurance_company}</span>
                        <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: "USD",
                            maximumFractionDigits: 0,
                          }).format(parseFloat((pip.pip_amount || pip.medpay_amount) || "0"))}
                        </Badge>
                      </div>
                      {pip.policy_number && (
                        <div className="flex justify-between text-sm">
                          <span className="text-sm text-gray-500">Policy Number</span>
                          <span className="font-medium">{pip.policy_number ? <CopyNumber value={pip.policy_number} /> : "—"}</span>
                        </div>
                      )}
                      {pip.claim_number && (
                        <div className="flex justify-between text-sm">
                          <span className="text-sm text-gray-500">Claim Number</span>
                          <span className="font-medium">{pip.claim_number ? <CopyNumber value={pip.claim_number} /> : "—"}</span>
                        </div>
                      )}
                      {pip.pip_amount && (
                        <div className="flex justify-between text-sm">
                          <span className="text-xs text-gray-500">PIP Amount</span>
                          <span className="font-semibold text-green-600">
                            {new Intl.NumberFormat("en-US", {
                              style: "currency",
                              currency: "USD",
                              maximumFractionDigits: 0,
                            }).format(parseFloat(pip.pip_amount))}
                          </span>
                        </div>
                      )}
                      {pip.medpay_amount && (
                        <div className="flex justify-between text-sm">
                          <span className="text-xs text-gray-500">MedPay Amount</span>
                          <span className="font-semibold text-green-600">
                            {new Intl.NumberFormat("en-US", {
                              style: "currency",
                              currency: "USD",
                              maximumFractionDigits: 0,
                            }).format(parseFloat(pip.medpay_amount))}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={baseContentStyle}>
                <div className="text-gray-500">No PIP/MedPay coverage</div>
              </div>
            )}
          </div>
        );
      }
      case "CASE_COST": {
        const data = card.data as CaseCostData;
        return (
          <div className="space-y-3">
            <h4 className="font-sm text-gray-900">Case Cost Details</h4>
            <div className={baseContentStyle}>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Total Amount</span>
                  <span className="font-semibold text-green-600">{formatCurrency(data.total_amount)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Paid Amount</span>
                  <span className="font-semibold text-green-600">{formatCurrency(data.paid_amount)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Pending Amount</span>
                  <span className="font-semibold text-orange-600">{formatCurrency(data.pending_amount)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Last Updated</span>
                  <span className="text-sm text-gray-600">{new Date(data.last_updated).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>
        );
      }
      default:
        return null;
    }
  };

  const details = getCardDetails(card);
  const detailedContent = getDetailedContent(card);

  const getCardStyle = (): string => {
    switch (card.card_type) {
      case "ATTORNEY_LIENS":
      case "MISC_LIENS":
      case "LOST_WAGES":
      case "PENDING_STATUTE":
      case "HEALTH_INSURANCE":
      case "PIP_MEDPAY":
        return "bg-emerald-50/80 hover:bg-emerald-50/90 transition-all duration-200 backdrop-blur-sm border-emerald-100";
      case "CASE_AGE":
        return "bg-purple-50/80 hover:bg-purple-50/90 transition-all duration-200 backdrop-blur-sm border-purple-100";
      case "POLICE_REPORT_STATUS":
        return "bg-red-50/80 hover:bg-red-50/90 transition-all duration-200 backdrop-blur-sm border-red-100";
      case "MEDICAL_TREATMENT":
        return "bg-blue-50/80 hover:bg-blue-50/90 transition-all duration-200 backdrop-blur-sm border-blue-100";
      case "PROPERTY_DAMAGE":
        return "bg-amber-50/80 hover:bg-amber-50/90 transition-all duration-200 backdrop-blur-sm border-amber-100";
      case "THIRD_PARTY_POLICY_LIMIT":
        return "bg-indigo-50/80 hover:bg-indigo-50/90 transition-all duration-200 backdrop-blur-sm border-indigo-100";
      case "ADVANCED_LOANS":
        return "bg-cyan-50/80 hover:bg-cyan-50/90 transition-all duration-200 backdrop-blur-sm border-cyan-100";
      case "CLIENT_AGE":
        return "bg-violet-50/80 hover:bg-violet-50/90 transition-all duration-200 backdrop-blur-sm border-violet-100";
      case "CASE_COST":
        return "bg-green-50/80 hover:bg-green-50/90 transition-all duration-200 backdrop-blur-sm border-green-100";
      default:
        return "bg-gray-50/80 hover:bg-gray-50/90 transition-all duration-200 backdrop-blur-sm border-gray-100";
    }
  };

  const handleCardClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (onClick && !e.defaultPrevented) {
      onClick();
    }
  };

  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <Card
          className={`${getCardStyle()} ${onClick ? "cursor-pointer transform hover:scale-[1.02]" : ""
            } relative group w-full h-[140px]`}
          onClick={handleCardClick}
        >
          <CardContent className="p-0 relative w-full h-full">
            <div className="p-1">
              <CardVisibilityMenu
                card={card}
                caseId={caseId}
                onVisibilityChange={onVisibilityChange}
              />

              <div className="flex flex-col gap-4">
                {/* Icon based on card type */}
                <div className="h-5 w-5 text-gray-600 ml-1">
                  {createElement(getCardIcon(card.card_type), { className: "h-full w-full" })}
                </div>
                <div className="space-y-1">
                  {/* Value */}
                  <div className={`text-sm font-semibold ${getValueColor()}`}>
                    {details.value}
                  </div>

                  {/* Title and Description */}
                  <div className="flex justify-between items-center">
                    <div className="text-xs text-gray-600">
                      {card.title}
                    </div>
                    {details.subtext && (
                      <div className="text-xs text-gray-500 opacity-50">
                        {details.subtext}
                      </div>
                    )}
                  </div>

                  <div className="flex items-end justify-end">
                    {/* Status Badge if exists */}
                    {/* <div className="flex-1">
                      {details.status && (
                        <div className="mt-1">
                          <Badge variant="outline" className={cn(
                            "font-medium",
                            details.status === "critical" ? "bg-red-50 text-red-600 border-red-200" :
                              details.status === "warning" ? "bg-orange-50 text-orange-600 border-orange-200" :
                                "bg-emerald-50 text-emerald-600 border-emerald-200"
                          )}>
                            {details.status}
                          </Badge>
                        </div>
                      )}
                    </div>  */}
                    {/* Details indicator always aligned to the right */}
                    {detailedContent && (
                      <div className="ml-auto">
                        <div className="text-xs text-gray-400 italic flex items-center gap-1">
                          Details <span className="text-[10px]">↓</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </HoverCardTrigger>
      {detailedContent && (
        <HoverCardContent
          side="top"
          align="center"
          sideOffset={12}
          className="w-80 shadow-lg border-gray-200/50 bg-white/95 backdrop-blur-sm z-[60]"
          style={{
            position: 'relative',
            zIndex: 60
          }}
        >
          {detailedContent}
        </HoverCardContent>
      )}
    </HoverCard>
  );
}

/**
 * Utility to sum original bills from a list of TreatmentProvider and format as USD.
 * @param providers Array of TreatmentProvider
 * @returns string formatted as currency
 */
function getTotalOriginalBills(providers: TreatmentProvider[] = []): string {
  const total = providers.reduce((sum, p) => sum + parseFloat(p.original_bill || '0'), 0);
  return total.toLocaleString('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 2 });
}

export function CaseDetail({
  caseId,
  setActiveTab,
}: CaseDetailProps) {
  const {
    data: combinedCards,
    isLoading,
    refetch,
  } = useCombinedCardsQuery(caseId);

  const { data: incidentDetails } = useIncidentDetailsQuery(caseId);

  const { data: pinnedFile, refetch: refetchPinnedFiles } = usePinnedFileQuery(caseId);
  const pinFile = usePinFileMutation();
  const [isEditingVideoUrl, setIsEditingVideoUrl] = useState(false);
  const [videoUrl, setVideoUrl] = useState("");
  const { data: accidentVideo } = useAccidentRecreationVideoQuery(caseId);
  const updateAccidentVideo = useUpdateAccidentRecreationVideoMutation();
  const createAccidentVideo = useCreateAccidentRecreationVideoMutation();
  const [selectedImage, setSelectedImage] = useState<{ url: string; name: string } | null>(null);
  // const { data: linkedCasesData } = useLinkedCasesQuery(caseId);
  // const [linkedCases, setLinkedCases] = useState<string[]>([]);
  // const [syncingCardId, setSyncingCardId] = useState<number | null>(null);

  // // Extract linked case IDs
  // useEffect(() => {
  //   if (linkedCasesData?.direct_cases && linkedCasesData.direct_cases.length > 0) {
  //     const linkedCaseIds = linkedCasesData.direct_cases.map(linkedCase => linkedCase.id);
  //     setLinkedCases(linkedCaseIds);
  //   }
  // }, [linkedCasesData]);

  // const isDocumentSidebarVisible = useSelector((state: RootState) => state.auth.isDocumentSidebarVisible);
  // const isTabsPanelCollapsed = useSelector((state: RootState) => state.auth.isTabsPanelCollapsed);
  // const isBothSidebarClosed = !isDocumentSidebarVisible && isTabsPanelCollapsed;

  // const deleteUserCard = useDeleteUserCardMutation(caseId);
  // const updateUserCard = useUpdateUserCardMutation(caseId);
  // const syncUserCard = useSyncUserCardMutation(caseId);
  const bulkUpdateOrders = useBulkUpdateCardOrdersMutation(caseId);
  const { toast } = useToast();
  const { data: treatmentProviders } = useTreatmentProvidersQuery(caseId);

  const handleUnpinFile = async (file: { id: string; name: string }) => {
    try {
      await pinFile.mutateAsync({
        case_id: caseId,
        file_id: file.id,
        unpin: true,
        file_name: file.name
      });
      await refetchPinnedFiles();
      toast({
        title: "Success",
        description: "File unpinned successfully",
      });
    } catch (error) {
      console.error('Error unpinning file:', error);
      toast({
        title: "Error",
        description: "Failed to unpin file",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (accidentVideo?.accident_recreation_video_url) {
      setVideoUrl(accidentVideo.accident_recreation_video_url);
    }
  }, [accidentVideo?.accident_recreation_video_url]);

  const handleVideoUrlSave = () => {
    if (!caseId) return;

    if (!videoUrl.trim()) {
      toast({
        title: "Error",
        description: "Please enter a valid URL",
        variant: "destructive",
      });
      return;
    }

    const videoData = { accident_recreation_video_url: videoUrl.trim() };

    if (accidentVideo?.accident_recreation_video_url) {
      updateAccidentVideo.mutate(
        { caseId, videoData },
        {
          onSuccess: () => {
            setIsEditingVideoUrl(false);
            toast({ title: "Success", description: "Link updated successfully" });
          },
          onError: () => {
            toast({ title: "Error", description: "Failed to update link", variant: "destructive" });
          },
        }
      );
    } else {
      createAccidentVideo.mutate(
        { caseId, videoData },
        {
          onSuccess: () => {
            setIsEditingVideoUrl(false);
            toast({ title: "Success", description: "Link added successfully" });
          },
          onError: () => {
            toast({ title: "Error", description: "Failed to add link", variant: "destructive" });
          },
        }
      );
    }
  };

  const isImageFile = (fileName: string): boolean => {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const lowerFileName = fileName.toLowerCase();
    return imageExtensions.some(ext => lowerFileName.endsWith(ext));
  };

  const handleOpenInNewTab = (url: string) => {
    window.open(url, '_blank');
  };

  const handleImageClick = (file: { view_url: string; name: string }) => {
    setSelectedImage({ url: file.view_url, name: file.name });
  };

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px of movement required before drag starts
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Sort and organize cards
  const { pinnedCards, systemCards, staticSystemCards, userCards } =
    useMemo(() => {
      if (!combinedCards)
        return {
          pinnedCards: [],
          systemCards: [],
          staticSystemCards: [],
          userCards: [],
        };

      // Filter out any cards without IDs first
      const validSystemCards = combinedCards.system_cards.filter(card => card.id);
      const validUserCards = combinedCards.user_cards.filter(card => card.id);

      const pinned = validUserCards
        .filter((card) => card.is_pinned)
        .sort((a, b) => (a.order ?? 0) - (b.order ?? 0));

      // Only include visible cards that have IDs
      const draggableSystem = validSystemCards
        .filter((card) => card.is_visible)
        .filter((card) => card.card_type !== "CASE_AGE" && card.card_type !== "CLIENT_AGE")
        .sort((a, b) => (a.order ?? 0) - (b.order ?? 0));

      // Remove staticSystem cards entirely as they don't have IDs
      const staticSystem: SystemCard[] = [];

      const user = validUserCards
        // .filter((card) => !card.is_pinned)
        .sort((a, b) => (a.order ?? 0) - (b.order ?? 0));

      return {
        pinnedCards: pinned,
        systemCards: draggableSystem,
        staticSystemCards: staticSystem,
        userCards: user,
      };
    }, [combinedCards]);

  // Get hidden system cards - only include cards with IDs
  const hiddenSystemCards = useMemo(() => {
    if (!combinedCards) return [];
    return combinedCards.system_cards
      .filter(card => card.id && !card.is_visible);
  }, [combinedCards]);

  const handleCardClick = (card: SystemCard) => {
    if (!setActiveTab) return;

    switch (card.card_type) {
      case "THIRD_PARTY_POLICY_LIMIT":
        setActiveTab("Defendants");
        break;
      case "PENDING_STATUTE":
      case "POLICE_REPORT_STATUS":
        setActiveTab("Incident");
        break;
      case "ATTORNEY_LIENS":
      case "MISC_LIENS":
        setActiveTab("Liens");
        break;
      case "HEALTH_PROVIDERS":
        setActiveTab("Medical Treatment");
        break;
      case "HEALTH_INSURANCE":
        setActiveTab("Health Insurance");
        break;
      case "PIP_MEDPAY":
        setActiveTab("Client");
        break;
      case "LOST_WAGES":
        setActiveTab("Employment");
        break;
      case "ADVANCED_LOANS":
        setActiveTab("Settlement Advance");
        break;
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    try {
      const activeId = parseInt(active.id.toString());
      const overId = parseInt(over.id.toString());

      // Determine which list we're working with

      const isSystemCard = systemCards.some((card) => card.id === activeId);
      const isPinnedCard = pinnedCards.some((card) => card.id === activeId);

      if (isSystemCard) {
        const items = [...systemCards];
        const cardType = "system";
      } else if (isPinnedCard) {
        const items = [...pinnedCards];
        const cardType = "pinned";
      } else {
        const items = [...userCards];
        const cardType = "user";
      }

      const items = isSystemCard ? systemCards : isPinnedCard ? pinnedCards : userCards;
      const oldIndex = items.findIndex((item) => item.id === activeId);
      const newIndex = items.findIndex((item) => item.id === overId);

      if (oldIndex === -1 || newIndex === -1) return;

      const reorderedItems = arrayMove(items as UserCard[], oldIndex, newIndex);
      const updates: CardOrderUpdate[] = reorderedItems.map((card, index) => ({
        id: card.id,
        order: index,
      }));

      const bulkUpdate: BulkCardOrderUpdate = {
        cardType: isSystemCard ? "system" : "user",
        updates,
      };

      await bulkUpdateOrders.mutateAsync(bulkUpdate);
      await refetch();

      toast({
        title: "Success",
        description: "Card order updated successfully",
      });
    } catch {
      toast({
        title: "Error",
        description: "Failed to update card order",
        variant: "destructive",
      });
    }
  };

  // const handleDeleteUserCard = async (cardId: number): Promise<void> => {
  //   try {
  //     await deleteUserCard.mutateAsync(cardId);
  //     await refetch();
  //     toast({
  //       title: "Success",
  //       description: "Note deleted successfully",
  //     });
  //   } catch {
  //     toast({
  //       title: "Error",
  //       description: "Failed to delete note",
  //       variant: "destructive",
  //     });
  //   }
  // };

  // const handleTogglePin = async (
  //   cardId: number,
  //   isPinned: boolean
  // ): Promise<void> => {
  //   try {
  //     await updateUserCard.mutateAsync({
  //       cardId,
  //       data: { is_pinned: isPinned },
  //     });
  //     await refetch();
  //   } catch {
  //     toast({
  //       title: "Error",
  //       description: "Failed to update note",
  //       variant: "destructive",
  //     });
  //   }
  // };

  // const handleSyncUserCard = async (cardId: number): Promise<void> => {
  //   if (!linkedCases.length) {
  //     toast({
  //       title: "Warning",
  //       description: "No linked cases found to sync with",
  //       variant: "default",
  //     });
  //     return;
  //   }

  //   try {
  //     setSyncingCardId(cardId);
  //     await syncUserCard.mutateAsync({
  //       source_card_id: cardId,
  //       target_case_ids: linkedCases
  //     });
  //   } catch (error) {
  //     console.error('Failed to sync user card:', error);
  //   } finally {
  //     setSyncingCardId(null);
  //   }
  // };

  if (isLoading) {
    return (
      <div className="w-full flex flex-col gap-6">
        <div className="grid grid-cols-3 gap-6">
          {[...Array(12)].map((_, index) => (
            <Card key={index} className="bg-white">
              <CardContent className="pt-6">
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-8 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Separator className="w-full h-px bg-gray-200" />
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col items-start gap-4 self-stretch">

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >

        {/* User Cards and Add Card Button */}
        {/* <div className="space-y-4 flex justify-between items-start align-self-stretch flex-col w-full">
          <div className="pr-2 flex justify-between items-center w-full bg-gray-100 rounded-lg shadow-sm w-full border-b-2 border-gray-200">
            <div className="flex items-center gap-2 bg-gray-100 px-4 py-3">
              <NotebookPen className="w-5 h-5 text-[#060216]" />
              <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
                Case Highlights
              </h2>
            </div>
            <CreateCardDialog caseId={caseId} onSuccess={refetch} />
          </div>
          {userCards.length > 0 ? (
            <div className="w-full">
              <div className="grid grid-cols-2 gap-4">
                <SortableContext
                  items={userCards.slice(1).map((card) => card.id)}
                  strategy={verticalListSortingStrategy}
                >
                  {userCards.slice(1).map((card) => (
                    <SortableCard key={card.id} id={card.id}>
                      <UserCardItem
                        card={card}
                        onDelete={handleDeleteUserCard}
                        caseId={caseId}
                        onSuccess={refetch}
                        linkedCases={linkedCases}
                        onSync={handleSyncUserCard}
                        syncingCardId={syncingCardId}
                      />
                    </SortableCard>
                  ))}
                </SortableContext>
              </div>
            </div>
          ) : (
            <div className="w-full text-center py-8 rounded-md">
              <p className="text-gray-500">No case highlights found.</p>
            </div>
          )}
        </div> */}

        {/* Tasks Section */}
        {/* <div className="space-y-4 w-full">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm w-full border-b-2 border-gray-200">
              <NotebookPen className="w-5 h-5 text-[#060216]" />
              <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
                Tasks
              </h2>
            </div>
          </div>
          <TaskCards caseId={caseId} />
        </div> */}

        {/* System Cards */}
        <div className="space-y-4 w-full">
          <div className="flex items-center justify-between w-full">
            <div className="pr-2 flex justify-between items-center w-full bg-gray-100 rounded-lg shadow-sm w-full border-b-2 border-gray-200">
              <div className="flex items-center gap-2 bg-gray-100 px-4 py-3">
                <Folders className="w-5 h-5 text-[#060216]" />
                <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
                  Case Overview
                </h2>
              </div>
              <div className="flex items-center gap-4">
                {/* <div className="text-sm text-gray-600">
                  <CopyNumber value={`${caseId.replace(/^CASE-/, '').toUpperCase()}@bcc.alphalaw.io`} />
                </div> */}
                {/* Accident Recreation Button and Dialog */}
                <div className="mt-2 px-4">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full bg-[#E8F0EA] hover:bg-[#d8e5dc] text-[#4B5563] border-[#E8F0EA] hover:border-[#d8e5dc] mb-4 flex items-center gap-2"
                      >
                        <Video className="h-5 w-5 text-[#4B5563]" /> {/* Existing icon */}
                        <span>Accident Recreation Video</span>
                        <div className="flex items-center gap-1 ml-auto">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <span>
                                  {accidentVideo?.accident_recreation_video_url ? (
                                    <div className="h-2 w-2 bg-green-600 rounded-full" />
                                  ) : (
                                    <div className="h-2 w-2 bg-red-600 rounded-full" />
                                  )}
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                {accidentVideo?.accident_recreation_video_url
                                  ? "Link added"
                                  : "No link added"}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[500px]">
                      <DialogHeader>

                        <DialogTitle>Accident Recreation Video</DialogTitle>
                      </DialogHeader>
                      <div className="py-4">
                        {isEditingVideoUrl ? (
                          <div className="flex items-center gap-2">
                            <Input
                              value={videoUrl}
                              onChange={(e) => setVideoUrl(e.target.value)}
                              placeholder="Enter video URL"
                              className="flex-1"
                              autoFocus
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  handleVideoUrlSave();
                                } else if (e.key === "Escape") {
                                  setIsEditingVideoUrl(false);
                                  setVideoUrl(accidentVideo?.accident_recreation_video_url || "");
                                }
                              }}
                            />
                            <Button onClick={handleVideoUrlSave} size="sm" className="px-3">
                              <Check className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          <div className="relative group">
                            {accidentVideo?.accident_recreation_video_url ? (
                              <>
                                <div className="aspect-video w-full bg-black rounded-lg overflow-hidden group-hover:ring-2 group-hover:ring-blue-400 transition-all">
                                  <VideoPlayer url={accidentVideo.accident_recreation_video_url} />
                                </div>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-white shadow-md hover:bg-gray-100"
                                  onClick={() => setIsEditingVideoUrl(true)}
                                >
                                  <Edit className="h-4 w-4 mr-1" />
                                  Edit
                                </Button>
                              </>
                            ) : (
                              <div className="flex flex-col items-center justify-center gap-4 p-8 border-2 border-dashed rounded-lg">
                                <Video className="h-12 w-12 text-gray-400" />
                                <p className="text-sm text-gray-500">No video added yet</p>
                                <Button variant="outline" onClick={() => setIsEditingVideoUrl(true)}>
                                  Add Video URL
                                </Button>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
                <HiddenCardsMenu
                  hiddenCards={hiddenSystemCards}
                  caseId={caseId}
                  onVisibilityChange={refetch}
                />
              </div>
            </div>
            {/* <div className="flex items-center">
              <HiddenCardsMenu
                hiddenCards={hiddenSystemCards}
                caseId={caseId}
                onVisibilityChange={refetch}
              />
            </div> */}
          </div>
          <div className={`grid grid-cols-4 gap-2 w-full`}>
            {/* Draggable System Cards  */}
            <SortableContext
              items={systemCards.map((card) => card.id)}
              strategy={verticalListSortingStrategy}
            >
              {systemCards.map((card) => (
                <SortableCard key={card.id} id={card.id}>
                  <SystemCardItem
                    card={card}
                    onClick={() => handleCardClick(card)}
                    caseId={caseId}
                    onVisibilityChange={refetch}
                  />
                </SortableCard>
              ))}
            </SortableContext>

            {/* Static System Cards (non-draggable) */}
            {staticSystemCards.map((card) => (
              <div key={card.card_type} className="relative w-full">
                <SystemCardItem
                  card={card}
                  onClick={() => handleCardClick(card)}
                  caseId={caseId}
                  onVisibilityChange={refetch}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Pinned Images Section */}
        {pinnedFile?.pinned_files && pinnedFile.pinned_files.length > 0 && (
          <div className="w-full mb-4">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-[#060216] font-Manrope text-base font-semibold leading-5 tracking-[0.15px] [font-feature-settings:'liga'_off,'clig'_off]">
                Pinned Images
              </h2>
            </div>
            <div className="grid grid-cols-4 gap-2">
              {pinnedFile.pinned_files.map((file) => (
                <ContextMenu key={file.id}>
                  <ContextMenuTrigger>
                    <div className="h-40 relative bg-gray-50 rounded-lg shadow-sm border border-gray-100">
                      <ImageSlider
                        files={[file]}
                        onImageClick={handleImageClick}
                        onOpenInNewTab={handleOpenInNewTab}
                        isImageFile={isImageFile}
                      />
                      <div className="absolute bottom-0 left-0 right-0 bg-white/80 px-2 py-1 text-xs truncate text-center">
                        {file.name}
                      </div>
                    </div>
                  </ContextMenuTrigger>
                  <ContextMenuContent>
                    <ContextMenuItem onClick={() => handleUnpinFile(file)}>
                      <PinOff className="mr-2 h-4 w-4" />
                      Unpin File
                    </ContextMenuItem>
                  </ContextMenuContent>
                </ContextMenu>
              ))}
            </div>
          </div>
        )}

        {/* Tasks Section */}
        <div className="space-y-4 w-full">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm w-full border-b-2 border-gray-200">
              <NotebookPen className="w-5 h-5 text-[#060216]" />
              <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
                Tasks
              </h2>
            </div>
          </div>
          <TaskCards caseId={caseId} />
        </div>

        {/* Incident Summary Section */}
        <div className="w-full">
          <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200 cursor-pointer hover:bg-gray-50/80 transition-colors" onClick={() => setActiveTab && setActiveTab("Incident")}>
            <FileText className="w-5 h-5 text-[#060216]" />
            <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
              Incident Summary
            </h2>
          </div>
          <CaseIncidentSummary
            street1={incidentDetails?.street1 || ''}
            street2={incidentDetails?.street2 || ''}
            city={incidentDetails?.city || ''}
            state={incidentDetails?.state || ''}
            zip_code={incidentDetails?.zip_code || ''}
            incidentDescription={incidentDetails?.incident_description || '—'}
            affectedAreas={incidentDetails?.affected_areas || {}}
          />
        </div>

        {/* Client Details Section */}
        {/* <div className="w-full">
          <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200 cursor-pointer hover:bg-gray-50/80 transition-colors" onClick={() => setActiveTab && setActiveTab("Client")}>
            <UserRound className="w-5 h-5 text-[#060216]" />
            <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
              Client Details
            </h2>
          </div>
          <ContactDetails caseId={caseId} />
        </div> */}

        {/* Defense Insurance Section */}
        <div className="w-full">
          <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200 cursor-pointer hover:bg-gray-50/80 transition-colors" onClick={() => setActiveTab && setActiveTab("Defendants")}>
            <Shield className="w-5 h-5 text-[#060216]" />
            <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
              Defense Insurance
            </h2>
          </div>
          <DefendantInsurance caseId={caseId} />
        </div>

        {/* Plaintiff Insurance Section */}
        <div className="w-full">
          <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 border-b-2 border-gray-200 cursor-pointer hover:bg-gray-50/80 transition-colors" onClick={() => setActiveTab && setActiveTab("Client")}>
            <ShieldCheck className="w-5 h-5 text-[#060216]" />
            <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6">
              Plaintiff Insurance
            </h2>
          </div>
          <PlaintiffInsurance caseId={caseId} />
        </div>

        {/* Health Provider Section */}
        <div className="w-full">
          <div className="flex items-center gap-2 bg-gray-100 px-4 py-3 rounded-lg shadow-sm mb-4 justify-between border-b-2 border-gray-200 cursor-pointer hover:bg-gray-50/80 transition-colors" onClick={() => setActiveTab && setActiveTab("Medical Treatment")}>
            <div className="flex items-center gap-2">
              <Stethoscope className="w-5 h-5 text-[#060216]" />
              <h2 className="text-[#060216] font-Manrope text-lg font-semibold leading-6 mr-2">
                Health Provider
              </h2>
            </div>
            {treatmentProviders && (treatmentProviders as TreatmentProvider[]).length > 0 && (
              <span className="ml-2 text-base font-bold text-[#22C55E]" title="Total Original Bills">
                Total: {getTotalOriginalBills(treatmentProviders as TreatmentProvider[])}
              </span>
            )}
          </div>
          <HealthProvider caseId={caseId} />
        </div>

        <LitigationEvents caseId={caseId} />
      </DndContext>

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          imageUrl={selectedImage.url}
          imageName={selectedImage.name}
          onClose={() => setSelectedImage(null)}
        />
      )}
    </div>
  );
}
