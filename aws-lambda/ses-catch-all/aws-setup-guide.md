# AWS SES Catch-All Setup with SNS

## Architecture Flow
```
Email → SES → [S3 + SNS] → SQS → Django Backend
```

## AWS Configuration Steps

### 1. SES Email Receiving Rule
- **Rule Name**: catch-all-bcc-dev-alphalaw
- **Recipients**: bcc-dev.alphalaw.io
- **Actions**:
  1. **S3 Action**: Store in `ses-emails-bcc-dev-alphalaw` bucket
  2. **SNS Action**: Publish to `ses-emails-bcc-dev-alphalaw` topic

### 2. SNS Topic Setup
- **Topic ARN**: `arn:aws:sns:us-east-1:971422687536:ses-emails-bcc-dev-alphalaw`
- **Encoding**: Base64 (preserves all characters)
- **Policy**: Allow SES to publish

### 3. SQS Queue Setup (Recommended)
- **Queue Name**: `ses-emails-processing-queue`
- **Type**: Standard Queue
- **Visibility Timeout**: 300 seconds (5 minutes)
- **Message Retention**: 14 days
- **Dead Letter Queue**: `ses-emails-dlq` (for failed processing)

### 4. SNS Subscription
- **Protocol**: SQS
- **Endpoint**: SQS Queue ARN
- **Raw Message Delivery**: Disabled (to get SNS metadata)

## Required AWS Resources

### SNS Topic Policy
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ses.amazonaws.com"
      },
      "Action": "SNS:Publish",
      "Resource": "arn:aws:sns:us-east-1:971422687536:ses-emails-bcc-dev-alphalaw",
      "Condition": {
        "StringEquals": {
          "aws:Referer": "971422687536"
        }
      }
    }
  ]
}
```

### SQS Queue Policy
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "sns.amazonaws.com"
      },
      "Action": "sqs:SendMessage",
      "Resource": "arn:aws:sqs:us-east-1:971422687536:ses-emails-processing-queue",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "arn:aws:sns:us-east-1:971422687536:ses-emails-bcc-dev-alphalaw"
        }
      }
    }
  ]
}
```

## Django Backend Changes Needed

### 1. AWS Credentials
- Add SQS permissions to your Django service
- Configure AWS credentials (IAM role or access keys)

### 2. SQS Polling Service
- Create a management command or Celery task
- Poll SQS for new messages
- Process email data
- Delete processed messages

### 3. Environment Variables
```
AWS_SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/971422687536/ses-emails-processing-queue
AWS_REGION=us-east-1
```

## Message Format from SNS

When SNS publishes to SQS, the message will contain:
- SNS metadata (MessageId, Timestamp, etc.)
- SES email data (Base64 encoded)
- Email headers and content

## Next Steps

1. Complete SES rule with SNS action
2. Create SQS queue and subscribe to SNS
3. Set up queue policies
4. Implement Django SQS polling
5. Test with sample email
