import json
import boto3
import email
import logging
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON>ipart
from email.mime.text import MIMEText
from datetime import datetime
import re
import os

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
s3_client = boto3.client('s3')
ses_client = boto3.client('ses')

# Environment variables
BUCKET_NAME = os.environ.get('S3_BUCKET_NAME', 'your-ses-emails-bucket')
DJANGO_API_ENDPOINT = os.environ.get('DJANGO_API_ENDPOINT', 'https://backend-api.alphalaw.io')
API_KEY = os.environ.get('DJANGO_API_KEY', '')

def lambda_handler(event, context):
    """
    Lambda function to handle catch-all emails for *.@bcc-dev.alphalaw.io
    """
    try:
        logger.info(f"Received SES event: {json.dumps(event)}")
        
        # Process each record in the event
        for record in event.get('Records', []):
            if record.get('eventSource') == 'aws:ses':
                process_ses_email(record)
        
        return {
            'statusCode': 200,
            'body': json.dumps('Successfully processed emails')
        }
        
    except Exception as e:
        logger.error(f"Error processing SES event: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'body': json.dumps(f'Error: {str(e)}')
        }

def process_ses_email(record):
    """
    Process individual SES email record
    """
    try:
        ses_mail = record['ses']['mail']
        message_id = ses_mail['messageId']
        
        logger.info(f"Processing email with message ID: {message_id}")
        
        # Get email details
        recipients = ses_mail.get('destination', [])
        sender = ses_mail.get('source', '')
        timestamp = ses_mail.get('timestamp', '')
        
        logger.info(f"Email from: {sender}, to: {recipients}")
        
        # Download email from S3
        s3_object_key = f"emails/{message_id}"
        
        try:
            response = s3_client.get_object(Bucket=BUCKET_NAME, Key=s3_object_key)
            raw_email = response['Body'].read()
            
            # Parse email
            email_message = email.message_from_bytes(raw_email)
            
            # Extract email details
            subject = email_message.get('Subject', '')
            from_address = email_message.get('From', '')
            to_addresses = email_message.get('To', '')
            cc_addresses = email_message.get('Cc', '')
            date_header = email_message.get('Date', '')
            
            # Get email body
            body = extract_email_body(email_message)
            
            # Extract case ID from recipient addresses
            case_id = extract_case_id_from_recipients(recipients)
            
            logger.info(f"Extracted case ID: {case_id} from recipients: {recipients}")
            
            # Prepare email data for Django API
            email_data = {
                'message_id': message_id,
                'subject': subject,
                'from_address': from_address,
                'to_addresses': to_addresses,
                'cc_addresses': cc_addresses,
                'body': body,
                'case_id': case_id,
                'timestamp': timestamp,
                'raw_email_s3_key': s3_object_key
            }
            
            # Send to Django API
            send_to_django_api(email_data)
            
        except Exception as e:
            logger.error(f"Error downloading email from S3: {str(e)}")
            # If S3 download fails, try to extract what we can from the SES event
            case_id = extract_case_id_from_recipients(recipients)
            
            email_data = {
                'message_id': message_id,
                'subject': 'Email processing error',
                'from_address': sender,
                'to_addresses': ', '.join(recipients),
                'body': f'Error processing email: {str(e)}',
                'case_id': case_id,
                'timestamp': timestamp,
                'error': str(e)
            }
            
            send_to_django_api(email_data)
            
    except Exception as e:
        logger.error(f"Error processing SES email record: {str(e)}", exc_info=True)

def extract_email_body(email_message):
    """
    Extract email body from email message
    """
    body = ""
    
    if email_message.is_multipart():
        for part in email_message.walk():
            content_type = part.get_content_type()
            content_disposition = str(part.get("Content-Disposition"))
            
            # Skip attachments
            if "attachment" not in content_disposition:
                if content_type == "text/plain":
                    body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    break
                elif content_type == "text/html" and not body:
                    body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
    else:
        body = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
    
    return body

def extract_case_id_from_recipients(recipients):
    """
    Extract case ID from recipient email addresses
    Format: <EMAIL> -> extract the 'anything' part
    """
    case_id = None
    
    for recipient in recipients:
        # Look for bcc-dev.alphalaw.io addresses
        if '@bcc-dev.alphalaw.io' in recipient.lower():
            # Extract the part before @
            match = re.search(r'([^@]+)@bcc-dev\.alphalaw\.io', recipient, re.IGNORECASE)
            if match:
                routing_id = match.group(1)
                # Convert to case ID format if it looks like a case routing ID
                if routing_id and routing_id != 'catch-all':
                    case_id = f"CASE-{routing_id.upper()}"
                    logger.info(f"Extracted case ID: {case_id} from recipient: {recipient}")
                    break
    
    return case_id

def send_to_django_api(email_data):
    """
    Send email data to Django API endpoint
    """
    try:
        import urllib3
        import json
        
        http = urllib3.PoolManager()
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {API_KEY}' if API_KEY else '',
        }
        
        # API endpoint for processing catch-all emails
        api_url = f"{DJANGO_API_ENDPOINT}/api/v1/emails/process-catch-all/"
        
        response = http.request(
            'POST',
            api_url,
            body=json.dumps(email_data),
            headers=headers
        )
        
        if response.status == 200:
            logger.info(f"Successfully sent email data to Django API for message: {email_data['message_id']}")
        else:
            logger.error(f"Failed to send email data to Django API. Status: {response.status}, Response: {response.data}")
            
    except Exception as e:
        logger.error(f"Error sending email data to Django API: {str(e)}", exc_info=True)
